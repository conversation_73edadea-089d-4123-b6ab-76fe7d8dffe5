import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/training_result.dart';

/// 结果页面
class ResultScreen extends StatelessWidget {
  final TrainingResult result;

  const ResultScreen({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('训练结果'),
        backgroundColor: const Color(AppConstants.primaryColorValue),
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false, // 隐藏返回按钮
      ),
      body: Column(
        children: [
          // 可滚动的上半部分
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 成绩卡片
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingLarge),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 2,
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // 正确率
                        Text(
                          '${result.correctRate.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: _getScoreColor(result.correctRate),
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingSmall),
                        Text(
                          '正确率',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingSmall),
                        Text(
                          '${result.correctCount}/${result.totalCount} 题正确',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // 详细统计
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '详细统计',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingSmall),
                        _buildStatRow('难度等级', AppConstants.difficultyNames[result.difficulty] ?? '未知'),
                        _buildStatRow('总用时', _formatTime(result.totalTimeSeconds)),
                        _buildStatRow('平均每题用时', '${result.averageTimePerQuestion.toStringAsFixed(1)} 秒'),
                        _buildStatRow('训练时间', _formatDateTime(result.date)),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  // 答题回顾 - 现在不使用 Expanded，而是让内容自然展开
                  if (result.questionResults.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '答题回顾',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: AppConstants.paddingSmall),
                          // 使用 Column 替代 ListView，让所有题目都显示出来
                          ...result.questionResults.asMap().entries.map((entry) {
                            final index = entry.key;
                            final questionResult = entry.value;
                            final isWrong = !questionResult.isCorrect;

                            return Container(
                              margin: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
                              padding: const EdgeInsets.all(AppConstants.paddingSmall),
                              decoration: BoxDecoration(
                                color: isWrong
                                    ? const Color(AppConstants.errorColorValue).withValues(alpha: 0.1)
                                    : Colors.grey[50],
                                borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                                border: isWrong
                                    ? Border.all(
                                        color: const Color(AppConstants.errorColorValue).withValues(alpha: 0.3),
                                        width: 1,
                                      )
                                    : null,
                              ),
                              child: Row(
                                children: [
                                  // 题号和状态图标
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: questionResult.isCorrect
                                          ? const Color(AppConstants.accentColorValue)
                                          : const Color(AppConstants.errorColorValue),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Text(
                                        '${index + 1}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: AppConstants.paddingSmall),

                                  // 题目和答案
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${questionResult.expression} = ${questionResult.correctAnswer.toStringAsFixed(questionResult.correctAnswer == questionResult.correctAnswer.toInt() ? 0 : 1)}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        if (isWrong) ...[
                                          const SizedBox(height: 2),
                                          Text(
                                            '你的答案: ${questionResult.userAnswer?.toStringAsFixed(questionResult.userAnswer == questionResult.userAnswer?.toInt() ? 0 : 1) ?? '未答'}',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),

                                  // 用时
                                  Text(
                                    '${questionResult.timeSeconds}s',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // 固定在底部的反馈信息和按钮
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                top: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 鼓励信息
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: _getEncouragementColor(result.correctRate),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Text(
                    _getEncouragementText(result.correctRate),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // 按钮组
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).popUntil((route) => route.isFirst);
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(AppConstants.primaryColorValue),
                          side: const BorderSide(
                            color: Color(AppConstants.primaryColorValue),
                          ),
                          minimumSize: const Size(0, AppConstants.buttonHeight),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                          ),
                        ),
                        child: const Text('返回首页'),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).popUntil((route) => route.isFirst);
                          // 这里可以添加直接开始新训练的逻辑
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppConstants.primaryColorValue),
                          foregroundColor: Colors.white,
                          minimumSize: const Size(0, AppConstants.buttonHeight),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                          ),
                        ),
                        child: const Text('再来一次'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计行
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: AppConstants.bodyFontSize,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: AppConstants.bodyFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// 根据分数获取颜色
  Color _getScoreColor(double correctRate) {
    if (correctRate >= 90) {
      return const Color(AppConstants.accentColorValue); // 绿色
    } else if (correctRate >= 70) {
      return const Color(AppConstants.warningColorValue); // 橙色
    } else {
      return const Color(AppConstants.errorColorValue); // 红色
    }
  }

  /// 根据分数获取鼓励信息颜色
  Color _getEncouragementColor(double correctRate) {
    if (correctRate >= 90) {
      return const Color(AppConstants.accentColorValue);
    } else if (correctRate >= 70) {
      return const Color(AppConstants.warningColorValue);
    } else {
      return const Color(AppConstants.errorColorValue);
    }
  }

  /// 根据分数获取鼓励文本
  String _getEncouragementText(double correctRate) {
    if (correctRate >= 90) {
      return '🎉 太棒了！你的心算能力很强！';
    } else if (correctRate >= 70) {
      return '👍 不错的成绩，继续努力！';
    } else if (correctRate >= 50) {
      return '💪 还有进步空间，加油练习！';
    } else {
      return '📚 多练习会让你更进步！';
    }
  }

  /// 格式化时间
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return '${minutes}分${remainingSeconds}秒';
    } else {
      return '${remainingSeconds}秒';
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
