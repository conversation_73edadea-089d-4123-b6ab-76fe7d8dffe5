import 'package:flutter/material.dart';
import 'dart:async';
import '../constants/app_constants.dart';
import '../models/question.dart';
import '../models/training_result.dart';
import '../services/question_generator.dart';
import '../services/storage_service.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_card.dart';
import '../widgets/progress_indicators.dart';
import 'result_screen.dart';

/// 训练页面
class TrainingScreen extends StatefulWidget {
  final int difficulty;

  const TrainingScreen({super.key, required this.difficulty});

  @override
  State<TrainingScreen> createState() => _TrainingScreenState();
}

class _TrainingScreenState extends State<TrainingScreen>
    with TickerProviderStateMixin {
  List<Question> _questions = [];
  final List<QuestionResult> _questionResults = [];
  int _currentQuestionIndex = 0;
  Timer? _timer;
  int _remainingSeconds = AppConstants.timePerQuestionSeconds;
  DateTime? _trainingStartTime;

  String _currentAnswer = '';
  bool _isNegative = false;

  // 动画控制器
  late AnimationController _questionAnimationController;
  late AnimationController _progressAnimationController;
  late Animation<double> _questionScaleAnimation;
  late Animation<double> _questionOpacityAnimation;

  // 状态控制
  bool _isSubmitting = false; // 防止重复提交
  bool _isShowingInputHint = false; // 防止重复显示输入提示
  bool _isTrainingFinished = false; // 防止重复完成训练

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeTraining();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _questionAnimationController.dispose();
    _progressAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _questionAnimationController = AnimationController(
      duration: const Duration(milliseconds: AppConstants.animationDurationMs),
      vsync: this,
    );

    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: AppConstants.animationDurationMs),
      vsync: this,
    );

    _questionScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _questionAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    _questionOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _questionAnimationController,
        curve: Curves.easeIn,
      ),
    );
  }

  /// 初始化训练
  void _initializeTraining() {
    _questions = QuestionGenerator.generateQuestions(
      AppConstants.questionsPerTraining,
      widget.difficulty,
    );
    _trainingStartTime = DateTime.now();
    _startQuestion();
  }

  /// 开始新题目
  void _startQuestion() {
    debugPrint('=== _startQuestion 开始 ===');
    debugPrint('当前题目索引: $_currentQuestionIndex');

    setState(() {
      _remainingSeconds = AppConstants.timePerQuestionSeconds;
      _currentAnswer = '';
      _isNegative = false;
      _isSubmitting = false; // 重置提交状态
      _isShowingInputHint = false; // 重置输入提示状态
    });

    debugPrint('新题目状态已重置，提交状态: $_isSubmitting');

    // 启动题目动画
    _questionAnimationController.reset();
    _questionAnimationController.forward();

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        _timeUp();
      }
    });
  }

  /// 时间到
  void _timeUp() {
    _timer?.cancel();
    _submitAnswer(null); // 传入null表示超时未答
  }

  /// 提交答案
  void _submitAnswer(double? userAnswer) {
    debugPrint('=== _submitAnswer 开始 ===');
    debugPrint('当前题目索引: $_currentQuestionIndex');
    debugPrint('总题目数: ${_questions.length}');
    debugPrint('用户答案: $userAnswer');
    debugPrint('当前提交状态: $_isSubmitting');

    // 防止重复提交
    if (_isSubmitting) {
      debugPrint('已在提交中，跳过重复提交');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });
    debugPrint('设置提交状态为 true');

    _timer?.cancel();

    final currentQuestion = _questions[_currentQuestionIndex];
    final timeUsed = AppConstants.timePerQuestionSeconds - _remainingSeconds;
    final isCorrect =
        userAnswer != null &&
        (userAnswer - currentQuestion.answer).abs() < 0.001; // 处理浮点数精度问题

    debugPrint('题目: ${currentQuestion.expression}');
    debugPrint('正确答案: ${currentQuestion.answer}');
    debugPrint('答案是否正确: $isCorrect');

    // 记录题目结果
    _questionResults.add(
      QuestionResult(
        expression: currentQuestion.expression,
        correctAnswer: currentQuestion.answer,
        userAnswer: userAnswer,
        isCorrect: isCorrect,
        timeSeconds: timeUsed,
      ),
    );

    debugPrint('已记录题目结果，当前结果数量: ${_questionResults.length}');

    // 直接处理下一题，不使用动画反馈
    debugPrint('准备进入下一题');
    _proceedToNextQuestion();
  }

  /// 处理下一题逻辑
  void _proceedToNextQuestion() {
    debugPrint('=== _proceedToNextQuestion 开始 ===');
    debugPrint('当前题目索引: $_currentQuestionIndex');
    debugPrint('总题目数: ${_questions.length}');
    debugPrint('是否为最后一题: ${_currentQuestionIndex >= _questions.length - 1}');

    // 确保不会超出题目范围
    if (_currentQuestionIndex < _questions.length - 1) {
      debugPrint('不是最后一题，进入下一题');
      setState(() {
        _currentQuestionIndex++;
      });
      debugPrint('题目索引已更新为: $_currentQuestionIndex');
      _startQuestion();
    } else {
      // 最后一道题，完成训练
      debugPrint('这是最后一题，准备完成训练');
      _finishTraining();
    }
  }

  /// 完成训练
  void _finishTraining() async {
    debugPrint('=== _finishTraining 开始 ===');
    debugPrint('当前提交状态: $_isSubmitting');
    debugPrint('训练完成状态: $_isTrainingFinished');
    debugPrint('Widget mounted 状态: $mounted');
    debugPrint('训练开始时间: $_trainingStartTime');
    debugPrint('题目结果数量: ${_questionResults.length}');

    // 防止重复完成训练
    if (_isTrainingFinished) {
      debugPrint('训练已经完成，跳过重复调用');
      return;
    }

    // 设置训练完成标志
    _isTrainingFinished = true;
    debugPrint('设置训练完成标志为 true');

    // 停止计时器
    _timer?.cancel();
    debugPrint('计时器已停止');

    // 设置提交状态
    setState(() {
      _isSubmitting = true;
    });
    debugPrint('设置提交状态为 true');

    try {
      final totalTime = DateTime.now()
          .difference(_trainingStartTime!)
          .inSeconds;
      final correctCount = _questionResults
          .where((result) => result.isCorrect)
          .length;

      debugPrint('总用时: $totalTime 秒');
      debugPrint('正确题目数: $correctCount');
      debugPrint('总题目数: ${_questions.length}');

      // 确保结果数量不超过题目数量
      final validResults = _questionResults.take(_questions.length).toList();
      debugPrint('有效结果数量: ${validResults.length}');

      final trainingResult = TrainingResult(
        date: DateTime.now(),
        correctCount: correctCount,
        totalCount: _questions.length,
        totalTimeSeconds: totalTime,
        difficulty: widget.difficulty,
        questionResults: validResults,
      );

      debugPrint('训练结果对象已创建');

      // 保存结果
      debugPrint('开始保存训练结果...');
      await StorageService.saveTrainingResult(trainingResult);
      debugPrint('训练结果保存成功');

      // 跳转到结果页面
      if (mounted) {
        debugPrint('Widget 仍然 mounted，准备跳转到结果页面');
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => ResultScreen(result: trainingResult),
          ),
        );
        debugPrint('页面跳转调用完成');
      } else {
        debugPrint('Widget 已经 unmounted，无法跳转页面');
      }
    } catch (e) {
      debugPrint('完成训练时发生错误: $e');
      // 如果出现错误，重置提交状态并显示错误提示
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
        debugPrint('已重置提交状态');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('保存结果时出现错误，请重试'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    debugPrint('=== _finishTraining 结束 ===');
  }

  /// 处理提交按钮点击
  void _onSubmitPressed() {
    debugPrint('=== _onSubmitPressed 被调用 ===');
    debugPrint('当前提交状态: $_isSubmitting');
    debugPrint('当前答案: $_currentAnswer');

    // 防止重复提交
    if (_isSubmitting) {
      debugPrint('正在提交中，忽略点击');
      return;
    }

    if (_currentAnswer.isEmpty) {
      // 防止重复显示输入提示
      if (!_isShowingInputHint) {
        setState(() {
          _isShowingInputHint = true;
        });

        // 清除之前的提示
        ScaffoldMessenger.of(context).clearSnackBars();

        ScaffoldMessenger.of(context)
            .showSnackBar(
              const SnackBar(
                content: Text('请输入答案'),
                duration: Duration(seconds: 2),
              ),
            )
            .closed
            .then((_) {
              // 提示消失后重置状态
              if (mounted) {
                setState(() {
                  _isShowingInputHint = false;
                });
              }
            });
      }
      return;
    }

    final answerText = _isNegative ? '-$_currentAnswer' : _currentAnswer;
    final userAnswer = double.tryParse(answerText);
    if (userAnswer == null) {
      // 清除之前的提示
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入有效的数字')));
      return;
    }

    _submitAnswer(userAnswer);
  }

  /// 处理数字键盘输入
  void _onNumberPressed(String number) {
    setState(() {
      if (number == '.') {
        if (!_currentAnswer.contains('.')) {
          _currentAnswer += number;
        }
      } else {
        _currentAnswer += number;
      }
    });
  }

  /// 处理删除键
  void _onDeletePressed() {
    setState(() {
      if (_currentAnswer.isNotEmpty) {
        _currentAnswer = _currentAnswer.substring(0, _currentAnswer.length - 1);
      }
    });
  }

  /// 处理清除键
  void _onClearPressed() {
    setState(() {
      _currentAnswer = '';
      _isNegative = false;
    });
  }

  /// 处理正负号切换
  void _onToggleSignPressed() {
    setState(() {
      _isNegative = !_isNegative;
    });
  }

  /// 构建自定义数字键盘
  Widget _buildCustomKeyboard() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingSmall),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 第一行：7 8 9 删除
          Row(
            children: [
              _buildKeyboardButton('7', () => _onNumberPressed('7')),
              _buildKeyboardButton('8', () => _onNumberPressed('8')),
              _buildKeyboardButton('9', () => _onNumberPressed('9')),
              _buildKeyboardButton('⌫', _onDeletePressed, isSpecial: true),
            ],
          ),
          const SizedBox(height: 4),

          // 第二行：4 5 6 清除
          Row(
            children: [
              _buildKeyboardButton('4', () => _onNumberPressed('4')),
              _buildKeyboardButton('5', () => _onNumberPressed('5')),
              _buildKeyboardButton('6', () => _onNumberPressed('6')),
              _buildKeyboardButton('C', _onClearPressed, isSpecial: true),
            ],
          ),
          const SizedBox(height: 4),

          // 第三行：1 2 3 +/-
          Row(
            children: [
              _buildKeyboardButton('1', () => _onNumberPressed('1')),
              _buildKeyboardButton('2', () => _onNumberPressed('2')),
              _buildKeyboardButton('3', () => _onNumberPressed('3')),
              _buildKeyboardButton('±', _onToggleSignPressed, isSpecial: true),
            ],
          ),
          const SizedBox(height: 4),

          // 第四行：0 . 确认
          Row(
            children: [
              _buildKeyboardButton('0', () => _onNumberPressed('0')),
              _buildKeyboardButton('.', () => _onNumberPressed('.')),
              _buildConfirmButton(),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建键盘按钮
  Widget _buildKeyboardButton(
    String text,
    VoidCallback onPressed, {
    bool isSpecial = false,
    bool isConfirm = false,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: isConfirm
                ? const Color(AppConstants.accentColorValue)
                : isSpecial
                ? Colors.grey[300]
                : Colors.white,
            foregroundColor: isConfirm
                ? Colors.white
                : isSpecial
                ? Colors.black87
                : const Color(AppConstants.primaryColorValue),
            minimumSize: const Size(0, 44),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusMedium,
              ),
            ),
            elevation: isConfirm ? 2 : 1,
            side: BorderSide(
              color: isConfirm
                  ? const Color(AppConstants.accentColorValue)
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Text(
            text,
            style: TextStyle(
              fontSize: isConfirm ? 18 : 20,
              fontWeight: isConfirm ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建确认按钮（特殊处理，不使用Expanded包装）
  Widget _buildConfirmButton() {
    return Expanded(
      flex: 2,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: ElevatedButton(
          onPressed: _isSubmitting ? null : _onSubmitPressed, // 提交时禁用按钮
          style: ElevatedButton.styleFrom(
            backgroundColor: _isSubmitting
                ? Colors.grey[400]
                : const Color(AppConstants.accentColorValue),
            foregroundColor: Colors.white,
            minimumSize: const Size(0, 44),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusMedium,
              ),
            ),
            elevation: _isSubmitting ? 0 : 2,
            side: BorderSide(
              color: _isSubmitting
                  ? Colors.grey[400]!
                  : const Color(AppConstants.accentColorValue),
              width: 1,
            ),
          ),
          child: Text(
            _isSubmitting ? '提交中...' : '确认',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_questions.isEmpty) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final currentQuestion = _questions[_currentQuestionIndex];
    final progress = (_currentQuestionIndex + 1) / _questions.length;

    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          '${AppConstants.difficultyNames[widget.difficulty]} - 第${_currentQuestionIndex + 1}/${_questions.length}题',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        backgroundColor: const Color(AppConstants.primaryColorValue).withOpacity(0.95),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: AppConstants.getBackgroundGradient(),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.spaceLg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 现代化进度指示器
                CustomCard.basic(
                  padding: const EdgeInsets.all(AppConstants.spaceMd),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '进度',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: const Color(AppConstants.textSecondaryColorValue),
                            ),
                          ),
                          Text(
                            '${_currentQuestionIndex + 1}/${_questions.length}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(AppConstants.primaryColorValue),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppConstants.spaceSm),
                      CustomProgressIndicator(
                        value: progress,
                        height: 6,
                        valueColor: const Color(AppConstants.primaryColorValue),
                        backgroundColor: const Color(AppConstants.dividerColorValue),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.spaceLg),

                // 现代化倒计时显示
                CustomCard.basic(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.spaceLg,
                    vertical: AppConstants.spaceMd,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: _remainingSeconds <= 5
                          ? LinearGradient(
                              colors: [
                                const Color(AppConstants.errorColorValue),
                                const Color(AppConstants.errorColorValue).withOpacity(0.8),
                              ],
                            )
                          : AppConstants.getPrimaryGradient(),
                      borderRadius: BorderRadius.circular(AppConstants.radiusLg),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.spaceLg,
                      vertical: AppConstants.spaceMd,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _remainingSeconds <= 5 ? Icons.timer : Icons.access_time,
                          color: Colors.white,
                          size: AppConstants.iconSizeMedium,
                        ),
                        const SizedBox(width: AppConstants.spaceSm),
                        Text(
                          '$_remainingSeconds',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: AppConstants.spaceXs),
                        const Text(
                          '秒',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.spaceLg),

            // 题目和答案输入区域 - 使用 Expanded 让它占用可用空间
            Expanded(
              flex: 3,
              child: Center(
                child: AnimatedBuilder(
                  animation: _questionAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _questionScaleAnimation.value,
                      child: Opacity(
                        opacity: _questionOpacityAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(
                            AppConstants.paddingLarge,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(
                              AppConstants.borderRadiusLarge,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withValues(alpha: 0.1),
                                spreadRadius: 2,
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // 题目表达式
                              Text(
                                '${currentQuestion.expression} = ',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),

                              // 答案输入显示
                              Container(
                                constraints: const BoxConstraints(minWidth: 80),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppConstants.paddingSmall,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey[50],
                                  borderRadius: BorderRadius.circular(
                                    AppConstants.borderRadiusSmall,
                                  ),
                                  border: Border.all(
                                    color: const Color(
                                      AppConstants.primaryColorValue,
                                    ).withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  _currentAnswer.isEmpty
                                      ? '?'
                                      : '${_isNegative ? '-' : ''}$_currentAnswer',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: _currentAnswer.isEmpty
                                        ? Colors.grey[400]
                                        : const Color(
                                            AppConstants.primaryColorValue,
                                          ),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            // 自定义数字键盘 - 固定高度
            _buildCustomKeyboard(),
          ],
        ),
      ),
    );
  }
}
