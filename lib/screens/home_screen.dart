import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/storage_service.dart';
import '../models/training_result.dart';
import 'training_screen.dart';
import 'history_screen.dart';

/// 首页界面
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  int _selectedDifficulty = AppConstants.difficultyEasy; // 默认选择简单难度
  List<TrainingResult> _recentResults = [];
  bool _isLoading = true;

  // 动画控制器
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadRecentResults();
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    // 启动淡入动画
    _fadeAnimationController.forward();
  }

  /// 加载最近的训练结果
  Future<void> _loadRecentResults() async {
    try {
      final results = await StorageService.getTrainingResults();
      setState(() {
        _recentResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 开始训练
  void _startTraining() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TrainingScreen(difficulty: _selectedDifficulty),
      ),
    ).then((_) {
      // 训练结束后刷新历史记录
      _loadRecentResults();
    });
  }

  /// 查看历史记录
  void _viewHistory() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const HistoryScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
              // 应用标题
              const SizedBox(height: AppConstants.paddingXLarge),
              Text(
                AppConstants.appName,
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(AppConstants.primaryColorValue),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                '提升你的心算能力',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.paddingXLarge * 2),

              // 难度选择
              Text(
                '选择难度',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              _buildDifficultySelector(),

              const SizedBox(height: AppConstants.paddingXLarge),

              // 开始训练按钮
              ElevatedButton(
                onPressed: _startTraining,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(AppConstants.primaryColorValue),
                  foregroundColor: Colors.white,
                  minimumSize: const Size(
                    double.infinity,
                    AppConstants.buttonHeight * 1.2,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  ),
                  elevation: 2,
                ),
                child: Text(
                  '开始训练',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // 历史记录按钮
              OutlinedButton(
                onPressed: _viewHistory,
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(AppConstants.primaryColorValue),
                  side: const BorderSide(
                    color: Color(AppConstants.primaryColorValue),
                  ),
                  minimumSize: const Size(
                    double.infinity,
                    AppConstants.buttonHeight,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  ),
                ),
                child: const Text('查看历史记录'),
              ),

              const Spacer(),

              // 最近成绩预览
              if (!_isLoading && _recentResults.isNotEmpty) _buildRecentStats(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建难度选择器
  Widget _buildDifficultySelector() {
    return Row(
      children: AppConstants.difficultyNames.entries.map((entry) {
        final difficulty = entry.key;
        final name = entry.value;
        final description = AppConstants.difficultyDescriptions[difficulty] ?? '';
        final isSelected = _selectedDifficulty == difficulty;

        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedDifficulty = difficulty;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: AppConstants.animationDurationMs),
                curve: Curves.easeInOut,
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(AppConstants.primaryColorValue)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  border: Border.all(
                    color: isSelected
                        ? const Color(AppConstants.primaryColorValue)
                        : Colors.grey[300]!,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: isSelected ? Colors.white70 : Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建最近成绩统计
  Widget _buildRecentStats() {
    final latestResult = _recentResults.first;
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '最近成绩',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '正确率: ${latestResult.correctRate.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: Colors.grey[700],
                ),
              ),
              Text(
                '平均用时: ${latestResult.averageTimePerQuestion.toStringAsFixed(1)}秒',
                style: TextStyle(
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
