/// 应用常量
class AppConstants {
  // 应用信息
  static const String appName = '心算训练';
  static const String appVersion = '1.0.0';

  // 训练设置
  static const int questionsPerTraining = 10; // 每次训练的题目数量
  static const int timePerQuestionSeconds = 30; // 每道题的时间限制（秒）

  // 难度等级
  static const int difficultyEasy = 1;
  static const int difficultyMedium = 2;
  static const int difficultyHard = 3;

  // 难度名称映射
  static const Map<int, String> difficultyNames = {
    difficultyEasy: '简单',
    difficultyMedium: '中等',
    difficultyHard: '困难',
  };

  // 难度描述
  static const Map<int, String> difficultyDescriptions = {
    difficultyEasy: '个位数运算',
    difficultyMedium: '两位数运算',
    difficultyHard: '三位数或小数运算',
  };

  // 颜色主题
  static const int primaryColorValue = 0xFF2196F3; // 蓝色
  static const int accentColorValue = 0xFF4CAF50; // 绿色
  static const int errorColorValue = 0xFFFF5722; // 红色
  static const int warningColorValue = 0xFFFF9800; // 橙色

  // 动画时长
  static const int animationDurationMs = 300;
  static const int pageTransitionDurationMs = 250;

  // 文本样式
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;

  // 间距
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // 圆角
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;

  // 按钮尺寸
  static const double buttonHeight = 48.0;
  static const double buttonMinWidth = 120.0;

  // 输入框
  static const double inputFieldHeight = 56.0;
}
